import React, { createContext, useContext, useReducer, useEffect } from 'react';
import { Policy, PolicySearchFormData, Scenario, DashboardState, SelectedCustomerData, SelectedPolicyData } from '../types';
import { fetchAllowedIllustrationTypes, IllustrationTypesRequest } from '../services/illustrationService';
import { LocalStorageService } from '../services/localStorageService';

interface DashboardContextType extends DashboardState {
  setActiveTab: (tab: string) => void;
  setCurrentPolicy: (policy: Policy | null) => void;
  setSelectedCustomerData: (customerData: SelectedCustomerData | null) => void;
  setSelectedPolicyData: (policyData: SelectedPolicyData | null) => void;
  setPolicySearchFormData: (formData: PolicySearchFormData | null) => void;
  addScenario: (scenario: Scenario) => Promise<string | null>;
  updateScenario: (id: string, updates: Partial<Scenario>) => Promise<void>;
  deleteScenario: (id: string) => Promise<void>;
  deleteMultipleScenarios: (ids: string[]) => Promise<void>;
  toggleScenarioSelection: (id: string) => void;
  selectAllScenarios: (select: boolean) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  setAllowedIllustrationTypes: (typeIds: number[]) => void;
  fetchAllowedIllustrationTypes: (policyId: number, customerId: number, policyType?: string) => Promise<void>;
}

const DashboardContext = createContext<DashboardContextType | undefined>(undefined);

const initialState: DashboardState = {
  activeTab: 'dashboard',
  currentPolicy: null,
  scenarios: [],
  selectedScenarios: [],
  selectedCustomerData: null,
  selectedPolicyData: null,
  policySearchFormData: null,
  loading: false,
  error: null,
  allowedIllustrationTypes: [], // Start empty until backend filtering is applied
};

type DashboardAction =
  | { type: 'SET_ACTIVE_TAB'; payload: string }
  | { type: 'SET_CURRENT_POLICY'; payload: Policy | null }
  | { type: 'SET_SELECTED_CUSTOMER_DATA'; payload: SelectedCustomerData | null }
  | { type: 'SET_SELECTED_POLICY_DATA'; payload: SelectedPolicyData | null }
  | { type: 'SET_POLICY_SEARCH_FORM_DATA'; payload: PolicySearchFormData | null }
  | { type: 'ADD_SCENARIO'; payload: Scenario }
  | { type: 'SET_SCENARIOS'; payload: Scenario[] }
  | { type: 'UPDATE_SCENARIO'; payload: { id: string; updates: Partial<Scenario> } }
  | { type: 'DELETE_SCENARIO'; payload: string }
  | { type: 'DELETE_MULTIPLE_SCENARIOS'; payload: string[] }
  | { type: 'TOGGLE_SCENARIO_SELECTION'; payload: string }
  | { type: 'SET_SELECTED_SCENARIOS'; payload: string[] }
  | { type: 'SELECT_ALL_SCENARIOS'; payload: boolean }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'SET_ALLOWED_ILLUSTRATION_TYPES'; payload: number[] };

const dashboardReducer = (state: DashboardState, action: DashboardAction): DashboardState => {
  switch (action.type) {
    case 'SET_ACTIVE_TAB':
      return { ...state, activeTab: action.payload };
    case 'SET_CURRENT_POLICY':
      return { ...state, currentPolicy: action.payload };
    case 'SET_SELECTED_CUSTOMER_DATA':
      return { ...state, selectedCustomerData: action.payload };
    case 'SET_SELECTED_POLICY_DATA':
      return { ...state, selectedPolicyData: action.payload };
    case 'SET_POLICY_SEARCH_FORM_DATA':
      return { ...state, policySearchFormData: action.payload };
    case 'ADD_SCENARIO':
      return { ...state, scenarios: [...state.scenarios, action.payload] };
    case 'SET_SCENARIOS':
      return { ...state, scenarios: action.payload };
    case 'UPDATE_SCENARIO':
      return {
        ...state,
        scenarios: state.scenarios.map(scenario =>
          scenario.id === action.payload.id
            ? { ...scenario, ...action.payload.updates }
            : scenario
        ),
      };
    case 'DELETE_SCENARIO':
      return {
        ...state,
        scenarios: state.scenarios.filter(scenario => scenario.id !== action.payload),
        selectedScenarios: state.selectedScenarios.filter(id => id !== action.payload),
      };
    case 'DELETE_MULTIPLE_SCENARIOS':
      return {
        ...state,
        scenarios: state.scenarios.filter(scenario => !action.payload.includes(scenario.id)),
        selectedScenarios: [],
      };
    case 'TOGGLE_SCENARIO_SELECTION':
      return {
        ...state,
        selectedScenarios: state.selectedScenarios.includes(action.payload)
          ? state.selectedScenarios.filter(id => id !== action.payload)
          : [...state.selectedScenarios, action.payload],
      };
    case 'SET_SELECTED_SCENARIOS':
      return {
        ...state,
        selectedScenarios: action.payload,
      };
    case 'SELECT_ALL_SCENARIOS':
      return {
        ...state,
        selectedScenarios: action.payload ? state.scenarios.map(s => s.id) : [],
      };
    case 'SET_LOADING':
      return { ...state, loading: action.payload };
    case 'SET_ERROR':
      return { ...state, error: action.payload };
    case 'SET_ALLOWED_ILLUSTRATION_TYPES':
      return { ...state, allowedIllustrationTypes: action.payload };
    default:
      return state;
  }
};

export const DashboardProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(dashboardReducer, initialState);

  // Initialize with data from localStorage
  useEffect(() => {
    const initializeData = async () => {
      dispatch({ type: 'SET_LOADING', payload: true });
      dispatch({ type: 'SET_ERROR', payload: null });

      try {
        // Load scenarios from localStorage
        const scenarios = LocalStorageService.getScenarios();
        const selectedScenarios = LocalStorageService.getSelectedScenarios();

        dispatch({ type: 'SET_SCENARIOS', payload: scenarios });
        dispatch({ type: 'SET_SELECTED_SCENARIOS', payload: selectedScenarios });

        console.log('🔍 DashboardContext initialized with localStorage data:', { scenarios: scenarios.length, selected: selectedScenarios.length });

      } catch (error) {
        const errorMessage = error instanceof Error
          ? error.message
          : 'Failed to initialize dashboard';

        dispatch({ type: 'SET_ERROR', payload: errorMessage });
        console.error('Failed to initialize dashboard:', error);
      } finally {
        dispatch({ type: 'SET_LOADING', payload: false });
      }
    };

    initializeData();
  }, []);

  // Auto-save scenarios and selected scenarios to localStorage when they change
  useEffect(() => {
    if (state.scenarios.length > 0) {
      LocalStorageService.saveScenarios(state.scenarios);
    }
  }, [state.scenarios]);

  useEffect(() => {
    LocalStorageService.saveSelectedScenarios(state.selectedScenarios);
  }, [state.selectedScenarios]);

  return (
    <DashboardContext.Provider value={{
      ...state,
      setActiveTab: (tab: string) => dispatch({ type: 'SET_ACTIVE_TAB', payload: tab }),
      setCurrentPolicy: (policy: Policy | null) => dispatch({ type: 'SET_CURRENT_POLICY', payload: policy }),
      setSelectedCustomerData: (customerData: SelectedCustomerData | null) => dispatch({ type: 'SET_SELECTED_CUSTOMER_DATA', payload: customerData }),
      setSelectedPolicyData: (policyData: SelectedPolicyData | null) => dispatch({ type: 'SET_SELECTED_POLICY_DATA', payload: policyData }),
      setPolicySearchFormData: (formData: PolicySearchFormData | null) => dispatch({ type: 'SET_POLICY_SEARCH_FORM_DATA', payload: formData }),
      addScenario: async (scenario: Scenario) => {
        try {
          dispatch({ type: 'SET_LOADING', payload: true });

          // Save scenario to localStorage
          const scenarioId = LocalStorageService.createScenario(scenario);
          console.log('🔍 Scenario saved to localStorage with ID:', scenarioId);

          // Add to local state
          dispatch({ type: 'ADD_SCENARIO', payload: { ...scenario, id: scenarioId } });

          // Automatically select the newly created scenario
          dispatch({ type: 'TOGGLE_SCENARIO_SELECTION', payload: scenarioId });

          return scenarioId;
        } catch (error) {
          const errorMessage = error instanceof Error
            ? error.message
            : 'Failed to create scenario';
          dispatch({ type: 'SET_ERROR', payload: errorMessage });
          console.error('Failed to create scenario:', error);
          return null;
        } finally {
          dispatch({ type: 'SET_LOADING', payload: false });
        }
      },
      updateScenario: async (id: string, updates: Partial<Scenario>) => {
        try {
          dispatch({ type: 'SET_LOADING', payload: true });
          // Update in localStorage
          LocalStorageService.updateScenario(id, updates);
          dispatch({ type: 'UPDATE_SCENARIO', payload: { id, updates } });
        } catch (error) {
          const errorMessage = error instanceof Error
            ? error.message
            : 'Failed to update scenario';
          dispatch({ type: 'SET_ERROR', payload: errorMessage });
          console.error('Failed to update scenario:', error);
        } finally {
          dispatch({ type: 'SET_LOADING', payload: false });
        }
      },
      deleteScenario: async (id: string) => {
        try {
          dispatch({ type: 'SET_LOADING', payload: true });
          // Delete from localStorage
          LocalStorageService.deleteScenario(id);
          dispatch({ type: 'DELETE_SCENARIO', payload: id });
        } catch (error) {
          const errorMessage = error instanceof Error
            ? error.message
            : 'Failed to delete scenario';
          dispatch({ type: 'SET_ERROR', payload: errorMessage });
          console.error('Failed to delete scenario:', error);
        } finally {
          dispatch({ type: 'SET_LOADING', payload: false });
        }
      },
      deleteMultipleScenarios: async (ids: string[]) => {
        try {
          dispatch({ type: 'SET_LOADING', payload: true });
          // Delete from localStorage
          LocalStorageService.deleteMultipleScenarios(ids);
          dispatch({ type: 'DELETE_MULTIPLE_SCENARIOS', payload: ids });
        } catch (error) {
          const errorMessage = error instanceof Error
            ? error.message
            : 'Failed to delete scenarios';
          dispatch({ type: 'SET_ERROR', payload: errorMessage });
          console.error('Failed to delete multiple scenarios:', error);
        } finally {
          dispatch({ type: 'SET_LOADING', payload: false });
        }
      },
      toggleScenarioSelection: (id: string) => dispatch({ type: 'TOGGLE_SCENARIO_SELECTION', payload: id }),
      selectAllScenarios: (select: boolean) => dispatch({ type: 'SELECT_ALL_SCENARIOS', payload: select }),
      setLoading: (loading: boolean) => dispatch({ type: 'SET_LOADING', payload: loading }),
      setError: (error: string | null) => dispatch({ type: 'SET_ERROR', payload: error }),
      setAllowedIllustrationTypes: (typeIds: number[]) => dispatch({ type: 'SET_ALLOWED_ILLUSTRATION_TYPES', payload: typeIds }),
      fetchAllowedIllustrationTypes: async (policyId: number, customerId: number, policyType?: string) => {
        try {
          dispatch({ type: 'SET_LOADING', payload: true });
          dispatch({ type: 'SET_ERROR', payload: null });

          const requestData: IllustrationTypesRequest = {
            policyId,
            customerId,
            policyType,
          };

          const response = await fetchAllowedIllustrationTypes(requestData);

          console.log('🔍 API Response:', response.success ? '✅ SUCCESS' : '❌ FAILED', response.allowedTypeIds);

          if (response.success) {
            dispatch({ type: 'SET_ALLOWED_ILLUSTRATION_TYPES', payload: response.allowedTypeIds });
          } else {
            console.warn('❌ Backend failed:', response.message);
            dispatch({ type: 'SET_ERROR', payload: response.message || 'Failed to fetch allowed illustration types' });
            // Keep default types on error
            dispatch({ type: 'SET_ALLOWED_ILLUSTRATION_TYPES', payload: [1, 2, 3, 4, 5, 6] });
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to fetch allowed illustration types';
          dispatch({ type: 'SET_ERROR', payload: errorMessage });
          console.error('Error fetching allowed illustration types:', error);
          // Keep default types on error
          dispatch({ type: 'SET_ALLOWED_ILLUSTRATION_TYPES', payload: [1, 2, 3, 4, 5, 6] });
        } finally {
          dispatch({ type: 'SET_LOADING', payload: false });
        }
      },
    }}>
      {children}
    </DashboardContext.Provider>
  );
};

export const useDashboard = (): DashboardContextType => {
  const context = useContext(DashboardContext);
  if (!context) {
    throw new Error('useDashboard must be used within a DashboardProvider');
  }
  return context;
};